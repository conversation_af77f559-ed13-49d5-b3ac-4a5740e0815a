# 基于网络监听的关键修复说明

## 修复概述

基于DrissionPage网络监听获取的真实API数据，对采集引擎进行了以下关键修复：

## 1. API端点更新

### 修复前
```python
'search_api': 'https://h5api.m.goofish.com/h5/mtop.taobao.idlehome.home.webpc.feed/1.0/'
```

### 修复后
```python
'search_api': 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/'
```

**原因**: 网络监听发现真实的搜索API是 `mtop.taobao.idlemtopsearch.pc.search`

## 2. 签名算法修复

### 修复前
```python
sign_str = f"{timestamp}&{app_key}&{data}&{token}"
```

### 修复后
```python
sign_str = f"{token_part}&{timestamp}&{app_key}&{data}"
```

**原因**: 通过分析成功请求(STATUS_NORMAL)发现签名顺序应该是token在前

## 3. Token处理优化

### 新增功能
- 优先从浏览器实时获取token
- 正确处理_m_h5_tk格式的token
- 支持token的下划线分割处理

```python
if token and '_' in token:
    token_part = token.split('_')[0]
else:
    token_part = token
```

## 4. 搜索参数格式修正

### 修复前
```python
data = {
    "keyword": keyword,
    "page": page,
    "pageSize": 20,
    "sortType": "default"
}
```

### 修复后
```python
data = {
    "keyword": keyword,
    "pageNum": page,  # 改为pageNum
    "pageSize": 20,
    "sortType": "default",
    "categoryId": "",     # 新增
    "priceRange": "",     # 新增
    "location": ""        # 新增
}
```

## 5. Cookie管理增强

### 新增关键Cookie
- 添加了 `x5secdata` 到重要cookie列表
- 增强了cookie同步验证
- 改进了token获取的优先级

```python
core_cookies = ['_m_h5_tk', '_tb_token_', 't']
optional_cookies = ['_m_h5_tk_enc', 'cookie2', 'sgcookie', 'x5secdata']
```

## 6. 请求头优化

### 新增必要请求头
```python
headers = {
    'Referer': 'https://www.goofish.com/search',
    'X-Requested-With': 'XMLHttpRequest',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
}
```

## 7. 响应解析增强

### 新增状态检查
- 检查API返回的ret状态码
- 特殊错误处理(TOKEN_EMPTY, ILLEGAL_ACCESS等)
- 更详细的错误日志

```python
if 'ret' in data:
    ret_codes = data['ret']
    if isinstance(ret_codes, list) and len(ret_codes) > 0:
        ret_code = ret_codes[0]
        if 'SUCCESS' not in ret_code:
            # 错误处理逻辑
```

## 8. 基础参数配置

### 新增参数
```python
self.base_params = {
    'jsv': '2.7.2',
    'appKey': '********',
    'v': '1.0',
    'type': 'originaljson',
    'accountSite': 'xianyu',
    'dataType': 'json',
    'timeout': '20000',
    'sessionOption': 'AutoLoginOnly',
    'spm_cnt': 'a21ybx.search.0.0'  # 新增
}
```

## 关键发现

通过网络监听发现的重要信息：

1. **成功请求特征**: `s_status: "STATUS_NORMAL"`
2. **失败请求特征**: `s_status: "STATUS_NOT_EXISTED"`
3. **真实token格式**: `77b3d1313ea39ec76893592920aa1717_1753878327365`
4. **正确的API名称**: `mtop.taobao.idlemtopsearch.pc.search`

## 测试验证

运行 `test_fixed_collector.py` 来验证修复效果：

```bash
python test_fixed_collector.py
```

## 注意事项

1. 确保浏览器已登录闲鱼账号
2. 首次运行可能需要手动登录
3. Token会定期过期，需要重新获取
4. 请求频率不要过高，避免被限制

## 下一步优化建议

1. 实现自动token刷新机制
2. 添加更多的错误重试策略
3. 优化请求频率控制
4. 增加更多API端点支持
