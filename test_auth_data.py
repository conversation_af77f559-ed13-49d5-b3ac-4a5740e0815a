#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整认证数据保存和加载功能
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_auth_data_functionality():
    """测试认证数据功能"""
    logger = setup_logger()
    logger.info("🔍 开始测试认证数据保存和加载功能")
    
    try:
        # 初始化
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 检查是否存在保存的认证数据
        auth_file = Path("auth_data.json")
        if auth_file.exists():
            logger.info("📁 发现已保存的认证数据文件")
            
            # 加载认证数据
            auth_data = collector.load_complete_auth_data()
            
            if auth_data:
                logger.info("✅ 成功加载认证数据")
                
                # 显示认证数据摘要
                logger.info("📊 认证数据摘要:")
                logger.info(f"  保存时间: {auth_data.get('save_time', 'N/A')}")
                logger.info(f"  状态: {auth_data.get('status', 'N/A')}")
                
                # Cookies信息
                cookies = auth_data.get('cookies', {})
                logger.info(f"  Cookies数量: {len(cookies)}")
                
                # Token信息
                token_info = auth_data.get('token_info', {})
                stats = token_info.get('_statistics', {})
                logger.info(f"  Token数量: {stats.get('total_tokens', 0)}")
                logger.info(f"  主Token: {'✅' if stats.get('has_main_token') else '❌'}")
                logger.info(f"  用户Token: {'✅' if stats.get('has_user_token') else '❌'}")
                
                # API参数信息
                api_params = auth_data.get('api_params', {})
                logger.info(f"  API参数数量: {len(api_params)}")
                if api_params:
                    logger.info(f"  AppKey: {api_params.get('appKey', 'N/A')}")
                    logger.info(f"  API: {api_params.get('api', 'N/A')}")
                
                # 用户信息
                user_info = auth_data.get('user_info', {})
                logger.info(f"  用户昵称: {user_info.get('nick', 'N/A')}")
                logger.info(f"  用户ID: {user_info.get('userId', 'N/A')}")
                
                # 应用认证数据到session
                logger.info("🔄 正在应用认证数据到session...")
                if collector.apply_auth_data_to_session(auth_data):
                    logger.info("✅ 认证数据应用成功")
                    
                    # 测试构建搜索参数
                    logger.info("🔧 测试构建搜索参数...")
                    params = collector._build_search_params("手机", 1)
                    
                    logger.info("📋 构建的搜索参数:")
                    logger.info(f"  AppKey: {params.get('appKey')}")
                    logger.info(f"  API: {params.get('api')}")
                    logger.info(f"  JSV: {params.get('jsv')}")
                    logger.info(f"  Token长度: {len(params.get('sign', ''))}")
                    
                    # 测试API请求
                    logger.info("📡 测试API请求...")
                    import requests
                    
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json, text/plain, */*',
                        'Referer': 'https://www.goofish.com/',
                        'Origin': 'https://www.goofish.com'
                    }
                    
                    response = requests.get(
                        collector.api_config['search_api'],
                        params=params,
                        headers=headers,
                        timeout=15
                    )
                    
                    logger.info(f"📊 API响应状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            ret_codes = data.get('ret', [])
                            
                            if ret_codes:
                                if 'SUCCESS' in str(ret_codes[0]):
                                    logger.info("✅ API请求成功！认证数据有效")
                                elif 'TOKEN_EMPTY' in str(ret_codes[0]):
                                    logger.warning("⚠️ Token为空，需要重新登录")
                                elif 'TOKEN_INVALID' in str(ret_codes[0]):
                                    logger.warning("⚠️ Token无效，需要重新登录")
                                else:
                                    logger.info(f"ℹ️ API返回: {ret_codes[0]}")
                            else:
                                logger.info("✅ API请求成功，返回数据正常")
                                
                        except json.JSONDecodeError:
                            logger.warning("⚠️ API响应不是有效JSON")
                    else:
                        logger.warning(f"⚠️ API请求失败: {response.status_code}")
                        
                else:
                    logger.warning("❌ 认证数据应用失败")
                    
            else:
                logger.warning("❌ 认证数据加载失败或已过期")
                
        else:
            logger.info("📝 未找到认证数据文件")
            logger.info("💡 请先运行程序并登录以生成认证数据")
            
            # 显示如何生成认证数据的提示
            logger.info("\n📖 生成认证数据的步骤:")
            logger.info("1. 运行主程序: python main.py")
            logger.info("2. 点击登录按钮")
            logger.info("3. 在浏览器中完成登录")
            logger.info("4. 登录成功后会自动保存完整认证数据")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_auth_data_functionality()
