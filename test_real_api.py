#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于DrissionPage网络监听的真实API测试
"""

import sys
import time
import json
import hashlib
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logger

def test_real_api_request():
    """测试基于网络监听获取的真实API参数"""
    logger = setup_logger("test_real_api")
    
    # 从网络监听中获取的真实参数
    real_token = "77b3d1313ea39ec76893592920aa1717_1753878327365"
    app_key = "34839810"
    
    # 构建搜索数据 - 基于真实API请求
    data = {
        "keyword": "iPhone",
        "pageNum": 1,
        "pageSize": 20,
        "sortType": "default"
    }
    
    data_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    timestamp = str(int(time.time() * 1000))
    
    # 生成签名 - 使用从网络监听中发现的真实算法
    token_part = real_token.split('_')[0]  # 只使用下划线前的部分
    sign_str = f"{token_part}&{timestamp}&{app_key}&{data_str}"
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    
    # 构建请求参数 - 完全基于监听到的真实请求
    params = {
        'jsv': '2.7.2',
        'appKey': app_key,
        't': timestamp,
        'sign': sign,
        'v': '1.0',
        'type': 'originaljson',
        'accountSite': 'xianyu',
        'dataType': 'json',
        'timeout': '20000',
        'api': 'mtop.taobao.idlemtopsearch.pc.search',
        'sessionOption': 'AutoLoginOnly',
        'spm_cnt': 'a21ybx.search.0.0',
        'data': data_str
    }
    
    # 设置请求头 - 模拟真实浏览器
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.goofish.com/search?q=iPhone',
        'Origin': 'https://www.goofish.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    # 设置cookies - 基于真实浏览器cookies
    cookies = {
        '_m_h5_tk': real_token,
        '_tb_token_': 'e01b035e53d53',
        't': 'e733784ccf9f4b7fcb08e26bbf3dc644',
        'unb': '3325557607',
        'tracknick': 't_1497253753990_0962'
    }
    
    # 发送请求
    url = 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/'
    
    logger.info("=" * 60)
    logger.info("测试基于网络监听的真实API请求")
    logger.info("=" * 60)
    
    logger.info(f"请求URL: {url}")
    logger.info(f"Token: {real_token}")
    logger.info(f"Token部分: {token_part}")
    logger.info(f"签名字符串: {sign_str}")
    logger.info(f"生成签名: {sign}")
    logger.info(f"搜索关键词: {data['keyword']}")
    
    try:
        response = requests.get(
            url, 
            params=params, 
            headers=headers, 
            cookies=cookies,
            timeout=30
        )
        
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info("=" * 60)
                logger.info("API响应成功!")
                logger.info("=" * 60)
                
                # 检查响应状态
                if 'ret' in result:
                    ret_codes = result['ret']
                    logger.info(f"返回状态: {ret_codes}")
                    
                    if isinstance(ret_codes, list) and len(ret_codes) > 0:
                        ret_code = ret_codes[0]
                        if 'SUCCESS' in ret_code:
                            logger.info("✅ API调用成功!")
                            
                            # 解析商品数据
                            if 'data' in result and 'resultList' in result['data']:
                                products = result['data']['resultList']
                                logger.info(f"获取到 {len(products)} 个商品:")
                                
                                for i, product in enumerate(products[:3]):  # 只显示前3个
                                    title = product.get('title', 'N/A')
                                    price = product.get('price', 'N/A')
                                    want_count = product.get('wantCount', 'N/A')
                                    logger.info(f"  {i+1}. {title} - 价格: {price} - 想要: {want_count}")
                            else:
                                logger.warning("响应中没有找到商品数据")
                        else:
                            logger.error(f"❌ API调用失败: {ret_code}")
                            if 'TOKEN_EMPTY' in ret_code:
                                logger.error("Token为空，需要重新获取认证信息")
                            elif 'ILLEGAL_ACCESS' in ret_code:
                                logger.error("非法请求，可能需要更新签名算法")
                
                # 显示完整响应（截断）
                response_str = json.dumps(result, ensure_ascii=False, indent=2)
                if len(response_str) > 1000:
                    logger.info(f"完整响应（前1000字符）: {response_str[:1000]}...")
                else:
                    logger.info(f"完整响应: {response_str}")
                
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                logger.error(f"响应内容: {response.text[:500]}...")
        else:
            logger.error(f"❌ API请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}...")
            
    except Exception as e:
        logger.error(f"❌ 请求异常: {e}")
    
    return None

def main():
    """主函数"""
    logger = setup_logger("main")
    
    logger.info("开始测试基于DrissionPage网络监听的真实API")
    
    result = test_real_api_request()
    
    if result:
        logger.info("\n🎉 测试成功！API请求正常工作。")
    else:
        logger.error("\n❌ 测试失败！请检查错误日志。")

if __name__ == "__main__":
    main()
