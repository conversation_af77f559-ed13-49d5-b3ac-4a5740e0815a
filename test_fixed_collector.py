#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的采集引擎
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from utils.logger import setup_logger

def test_fixed_collector():
    """测试修复后的采集引擎"""
    logger = setup_logger("test_fixed")
    
    try:
        logger.info("=" * 50)
        logger.info("测试修复后的采集引擎")
        logger.info("=" * 50)
        
        # 创建采集引擎实例
        collector = CollectorEngine()
        
        # 初始化浏览器
        logger.info("1. 初始化浏览器...")
        if not collector.init_browser(headless=False):
            logger.error("❌ 浏览器初始化失败")
            return False
        
        logger.info("✅ 浏览器初始化成功")
        
        # 等待页面加载
        time.sleep(3)
        
        # 测试token获取
        logger.info("2. 测试token获取...")
        token = collector.get_token_from_cookies()
        if token:
            logger.info(f"✅ 成功获取token: {token[:20]}...")
        else:
            logger.warning("⚠️ 未获取到token，可能需要登录")
        
        # 测试cookies同步
        logger.info("3. 测试cookies同步...")
        collector.sync_cookies_to_session()
        
        # 测试搜索参数构建
        logger.info("4. 测试搜索参数构建...")
        params = collector._build_search_params("手机", 1)
        logger.info(f"✅ 搜索参数构建成功: {len(params)} 个参数")
        
        # 显示关键参数
        key_params = ['api', 'sign', 't', 'data']
        for key in key_params:
            if key in params:
                value = params[key]
                if len(str(value)) > 50:
                    logger.info(f"  {key}: {str(value)[:50]}...")
                else:
                    logger.info(f"  {key}: {value}")
        
        # 测试搜索功能（只测试一页）
        logger.info("5. 测试搜索功能...")
        try:
            results = collector.collect_search_data("手机", max_pages=1)
            
            if results:
                logger.info(f"✅ 搜索成功，获取到 {len(results)} 个商品")
                
                # 显示前3个商品信息
                for i, product in enumerate(results[:3]):
                    title = product.get('title', 'N/A')
                    price = product.get('price', 'N/A')
                    want_count = product.get('wantCount', 'N/A')
                    logger.info(f"  商品 {i+1}: {title} - 价格: {price} - 想要: {want_count}")
            else:
                logger.warning("⚠️ 搜索未返回结果")
                
        except Exception as e:
            logger.error(f"❌ 搜索测试失败: {e}")
        
        # 关闭浏览器
        logger.info("6. 清理资源...")
        collector.close()
        
        logger.info("=" * 50)
        logger.info("✅ 测试完成")
        logger.info("=" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    success = test_fixed_collector()
    
    if success:
        print("\n🎉 测试成功！修复后的采集引擎工作正常。")
    else:
        print("\n❌ 测试失败！请检查错误日志。")

if __name__ == "__main__":
    main()
