#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的API - 使用从网络监听中获取的真实参数
"""

import sys
import os
import time
import json
import hashlib
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from utils.logger import setup_logger

def test_real_api_with_token():
    """测试使用真实token的API请求"""
    logger = setup_logger("test_api")
    
    # 从网络监听中获取的真实token
    real_token = "77b3d1313ea39ec76893592920aa1717"
    
    # 构建搜索数据
    data = {
        "keyword": "手机",
        "pageNum": 1,
        "pageSize": 20,
        "sortType": "default",
        "categoryId": "",
        "priceRange": "",
        "location": ""
    }
    
    data_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    timestamp = str(int(time.time() * 1000))
    app_key = "********"
    
    # 生成签名
    sign_str = f"{timestamp}&{app_key}&{data_str}&{real_token}"
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    
    # 构建请求参数
    params = {
        'jsv': '2.7.2',
        'appKey': app_key,
        't': timestamp,
        'sign': sign,
        'v': '1.0',
        'type': 'originaljson',
        'accountSite': 'xianyu',
        'dataType': 'json',
        'timeout': '20000',
        'api': 'mtop.taobao.idlemtopsearch.pc.search',
        'sessionOption': 'AutoLoginOnly',
        'spm_cnt': 'a21ybx.search.0.0',
        'data': data_str
    }
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.goofish.com/',
        'Origin': 'https://www.goofish.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    # 发送请求
    url = 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/'
    
    logger.info(f"发送API请求到: {url}")
    logger.info(f"请求参数: {params}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info(f"API响应成功: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                logger.error(f"响应内容: {response.text}")
        else:
            logger.error(f"API请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            
    except Exception as e:
        logger.error(f"请求异常: {e}")
    
    return None

def test_collector_engine():
    """测试更新后的采集引擎"""
    logger = setup_logger("test_collector")
    
    try:
        # 创建采集引擎实例
        collector = CollectorEngine()
        
        # 初始化浏览器
        logger.info("初始化浏览器...")
        if not collector.init_browser(headless=False):
            logger.error("浏览器初始化失败")
            return False
        
        # 等待页面加载
        time.sleep(3)
        
        # 测试搜索功能
        logger.info("测试搜索功能...")
        results = collector.search_products("手机", max_pages=1)
        
        if results:
            logger.info(f"搜索成功，获取到 {len(results)} 个商品")
            for i, product in enumerate(results[:3]):  # 只显示前3个
                logger.info(f"商品 {i+1}: {product.get('title', 'N/A')}")
        else:
            logger.warning("搜索未返回结果")
        
        # 关闭浏览器
        collector.close()
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logger("main")
    
    logger.info("=" * 50)
    logger.info("开始测试更新后的API")
    logger.info("=" * 50)
    
    # 测试1: 直接API请求
    logger.info("\n1. 测试直接API请求...")
    api_result = test_real_api_with_token()
    
    if api_result:
        logger.info("✅ 直接API请求测试成功")
    else:
        logger.error("❌ 直接API请求测试失败")
    
    # 测试2: 采集引擎测试
    logger.info("\n2. 测试采集引擎...")
    engine_result = test_collector_engine()
    
    if engine_result:
        logger.info("✅ 采集引擎测试成功")
    else:
        logger.error("❌ 采集引擎测试失败")
    
    logger.info("\n" + "=" * 50)
    logger.info("测试完成")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
