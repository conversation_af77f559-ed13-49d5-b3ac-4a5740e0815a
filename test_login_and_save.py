#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录并保存完整认证数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_login_and_save():
    """测试登录并保存认证数据"""
    logger = setup_logger()
    logger.info("🔍 开始测试登录并保存认证数据")
    
    try:
        # 初始化
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 初始化浏览器
        logger.info("🌐 正在初始化浏览器...")
        if not collector.init_browser():
            logger.error("❌ 浏览器初始化失败")
            return
        
        logger.info("✅ 浏览器初始化成功")
        
        # 打开闲鱼首页
        logger.info("📱 正在打开闲鱼首页...")
        collector.page.get('https://www.goofish.com/')
        
        # 等待页面加载
        import time
        time.sleep(3)
        
        logger.info("✅ 闲鱼首页已打开")
        logger.info("🔑 请在浏览器中手动登录...")
        
        # 等待用户确认登录
        input("\n请在浏览器中完成登录，登录成功后按回车键继续...")
        
        # 检查登录状态
        logger.info("🔍 正在检查登录状态...")
        if collector.check_login_status():
            logger.info("✅ 登录状态验证成功！")
            
            # 保存完整认证数据
            logger.info("💾 正在保存完整认证数据...")
            if collector.save_complete_auth_data():
                logger.info("✅ 完整认证数据保存成功！")
                
                # 验证保存的数据
                logger.info("🔍 正在验证保存的数据...")
                auth_data = collector.load_complete_auth_data()
                
                if auth_data:
                    logger.info("✅ 认证数据验证成功！")
                    logger.info("📊 数据摘要:")
                    logger.info(f"  保存时间: {auth_data.get('save_time')}")
                    logger.info(f"  Cookies数量: {len(auth_data.get('cookies', {}))}")
                    logger.info(f"  Token数量: {len(auth_data.get('token_info', {}))}")
                    logger.info(f"  API参数数量: {len(auth_data.get('api_params', {}))}")
                    
                    user_info = auth_data.get('user_info', {})
                    logger.info(f"  用户信息: {user_info.get('nick', 'N/A')}")
                    
                    # 测试使用保存的数据构建搜索参数
                    logger.info("🔧 测试使用保存的数据构建搜索参数...")
                    params = collector._build_search_params("手机", 1)
                    
                    logger.info("📋 搜索参数构建成功:")
                    logger.info(f"  AppKey: {params.get('appKey')}")
                    logger.info(f"  API: {params.get('api')}")
                    logger.info(f"  有Token: {'是' if params.get('sign') else '否'}")
                    
                    logger.info("\n🎉 测试完成！现在你可以:")
                    logger.info("1. 运行 python test_auth_data.py 查看详细认证数据")
                    logger.info("2. 运行主程序进行商品采集")
                    logger.info("3. 认证数据将在7天内有效")
                    
                else:
                    logger.error("❌ 认证数据验证失败")
                    
            else:
                logger.error("❌ 完整认证数据保存失败")
                
        else:
            logger.warning("❌ 登录状态验证失败，请确保已正确登录")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        
    finally:
        # 清理资源
        try:
            if hasattr(collector, 'page') and collector.page:
                collector.page.quit()
                logger.info("🧹 浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    test_login_and_save()
