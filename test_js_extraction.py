#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript直接提取商品数据
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_js_extraction():
    """测试JavaScript直接提取功能"""
    logger = setup_logger("test_js")
    
    try:
        logger.info("=" * 60)
        logger.info("测试JavaScript直接提取商品数据")
        logger.info("=" * 60)
        
        # 创建配置管理器和采集引擎
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 初始化浏览器
        logger.info("1. 初始化浏览器...")
        if not collector.init_browser(headless=False):
            logger.error("❌ 浏览器初始化失败")
            return False
        
        logger.info("✅ 浏览器初始化成功")
        
        # 等待用户手动登录（如果需要）
        logger.info("2. 请在浏览器中手动登录闲鱼账号（如果需要）...")
        logger.info("登录完成后，按回车键继续...")
        input()
        
        # 测试JavaScript提取
        logger.info("3. 测试JavaScript提取...")
        try:
            products = collector._extract_products_with_js("iPhone", max_pages=1)
            
            if products:
                logger.info(f"✅ JavaScript提取成功，获取到 {len(products)} 个商品")
                
                # 显示商品信息
                for i, product in enumerate(products[:5]):  # 只显示前5个
                    logger.info(f"商品 {i+1}:")
                    logger.info(f"  标题: {product.get('title', 'N/A')}")
                    logger.info(f"  价格: {product.get('price', 'N/A')}")
                    logger.info(f"  想要: {product.get('wantCount', 'N/A')}")
                    logger.info(f"  位置: {product.get('location', 'N/A')}")
                    logger.info(f"  链接: {product.get('itemUrl', 'N/A')}")
                    logger.info("-" * 40)
                
                return True
            else:
                logger.warning("❌ JavaScript提取未获取到商品数据")
                return False
                
        except Exception as e:
            logger.error(f"❌ JavaScript提取失败: {e}")
            return False
        
        finally:
            # 关闭浏览器
            logger.info("4. 清理资源...")
            collector.close()
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def test_full_collection():
    """测试完整的采集流程（包含JavaScript回退）"""
    logger = setup_logger("test_full")
    
    try:
        logger.info("=" * 60)
        logger.info("测试完整采集流程")
        logger.info("=" * 60)
        
        # 创建配置管理器和采集引擎
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 初始化浏览器
        logger.info("1. 初始化浏览器...")
        if not collector.init_browser(headless=False):
            logger.error("❌ 浏览器初始化失败")
            return False
        
        logger.info("✅ 浏览器初始化成功")
        
        # 等待用户手动登录（如果需要）
        logger.info("2. 请在浏览器中手动登录闲鱼账号（如果需要）...")
        logger.info("登录完成后，按回车键继续...")
        input()
        
        # 测试完整采集流程
        logger.info("3. 测试完整采集流程...")
        try:
            products = collector.collect_search_data("iPhone", max_pages=1)
            
            if products:
                logger.info(f"✅ 采集成功，获取到 {len(products)} 个商品")
                
                # 显示商品信息
                for i, product in enumerate(products[:3]):  # 只显示前3个
                    logger.info(f"商品 {i+1}:")
                    logger.info(f"  标题: {product.get('title', 'N/A')}")
                    logger.info(f"  价格: {product.get('price', 'N/A')}")
                    logger.info(f"  想要: {product.get('wantCount', 'N/A')}")
                    logger.info("-" * 40)
                
                return True
            else:
                logger.warning("❌ 采集未获取到商品数据")
                return False
                
        except Exception as e:
            logger.error(f"❌ 采集失败: {e}")
            return False
        
        finally:
            # 关闭浏览器
            logger.info("4. 清理资源...")
            collector.close()
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logger("main")
    
    logger.info("开始测试JavaScript提取功能")
    
    # 测试1: 纯JavaScript提取
    logger.info("\n1. 测试纯JavaScript提取...")
    js_result = test_js_extraction()
    
    if js_result:
        logger.info("✅ JavaScript提取测试成功")
    else:
        logger.error("❌ JavaScript提取测试失败")
    
    # 测试2: 完整采集流程
    logger.info("\n2. 测试完整采集流程...")
    full_result = test_full_collection()
    
    if full_result:
        logger.info("✅ 完整采集测试成功")
    else:
        logger.error("❌ 完整采集测试失败")
    
    # 总结
    logger.info("\n" + "=" * 60)
    if js_result or full_result:
        logger.info("🎉 至少一种方法成功！")
        logger.info("\n解决方案:")
        if js_result:
            logger.info("✅ JavaScript直接提取: 成功绕过API限制")
        if full_result:
            logger.info("✅ 完整采集流程: 包含JavaScript回退机制")
    else:
        logger.error("❌ 所有测试都失败了")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
