#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试修复后的采集引擎 - 基于DrissionPage网络监听的真实数据
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_final_collector():
    """测试最终修复后的采集引擎"""
    logger = setup_logger("test_final")
    
    try:
        logger.info("=" * 60)
        logger.info("测试基于网络监听修复后的采集引擎")
        logger.info("=" * 60)
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建采集引擎实例
        collector = CollectorEngine(config_manager)
        
        # 初始化浏览器
        logger.info("1. 初始化浏览器...")
        if not collector.init_browser(headless=False):
            logger.error("❌ 浏览器初始化失败")
            return False
        
        logger.info("✅ 浏览器初始化成功")
        
        # 等待页面加载
        time.sleep(5)
        
        # 测试token获取
        logger.info("2. 测试token获取...")
        token = collector.get_token_from_cookies()
        if token:
            logger.info(f"✅ 成功获取token: {token[:30]}...")
            if '_' in token:
                token_part = token.split('_')[0]
                logger.info(f"Token部分: {token_part}")
        else:
            logger.warning("⚠️ 未获取到token，可能需要登录")
        
        # 测试cookies同步
        logger.info("3. 测试cookies同步...")
        collector.sync_cookies_to_session()
        
        # 测试搜索参数构建
        logger.info("4. 测试搜索参数构建...")
        try:
            params = collector._build_search_params("iPhone", 1)
            logger.info(f"✅ 搜索参数构建成功: {len(params)} 个参数")
            
            # 显示关键参数
            key_params = ['api', 'sign', 't', 'data']
            for key in key_params:
                if key in params:
                    value = params[key]
                    if len(str(value)) > 50:
                        logger.info(f"  {key}: {str(value)[:50]}...")
                    else:
                        logger.info(f"  {key}: {value}")
        except Exception as e:
            logger.error(f"❌ 搜索参数构建失败: {e}")
            return False
        
        # 测试搜索功能（只测试一页）
        logger.info("5. 测试搜索功能...")
        try:
            results = collector.collect_search_data("iPhone", max_pages=1)
            
            if results:
                logger.info(f"✅ 搜索成功，获取到 {len(results)} 个商品")
                
                # 显示前3个商品信息
                for i, product in enumerate(results[:3]):
                    title = product.get('title', 'N/A')
                    price = product.get('price', 'N/A')
                    want_count = product.get('wantCount', 'N/A')
                    logger.info(f"  商品 {i+1}: {title} - 价格: {price} - 想要: {want_count}")
            else:
                logger.warning("⚠️ 搜索未返回结果，可能需要登录或等待")
                
        except Exception as e:
            logger.error(f"❌ 搜索测试失败: {e}")
            logger.info("这可能是由于需要登录或API限制导致的")
        
        # 关闭浏览器
        logger.info("6. 清理资源...")
        collector.close()
        
        logger.info("=" * 60)
        logger.info("✅ 测试完成")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def test_direct_api():
    """测试直接API调用"""
    logger = setup_logger("test_direct_api")
    
    logger.info("=" * 60)
    logger.info("测试直接API调用（需要有效token）")
    logger.info("=" * 60)
    
    # 这里可以添加直接API调用的测试
    # 但需要有效的token和cookies
    logger.info("直接API调用需要有效的token和cookies")
    logger.info("建议先运行浏览器测试获取有效认证信息")
    
    return True

def main():
    """主函数"""
    logger = setup_logger("main")
    
    logger.info("开始最终测试")
    
    # 测试1: 采集引擎测试
    logger.info("\n1. 测试采集引擎...")
    engine_result = test_final_collector()
    
    if engine_result:
        logger.info("✅ 采集引擎测试成功")
    else:
        logger.error("❌ 采集引擎测试失败")
    
    # 测试2: 直接API测试
    logger.info("\n2. 测试直接API...")
    api_result = test_direct_api()
    
    if api_result:
        logger.info("✅ 直接API测试成功")
    else:
        logger.error("❌ 直接API测试失败")
    
    # 总结
    logger.info("\n" + "=" * 60)
    if engine_result and api_result:
        logger.info("🎉 所有测试通过！修复成功！")
        logger.info("\n修复要点:")
        logger.info("1. ✅ 使用正确的搜索API: mtop.taobao.idlemtopsearch.pc.search")
        logger.info("2. ✅ 修复签名算法: {token_part}&{timestamp}&{app_key}&{data}")
        logger.info("3. ✅ 正确处理token格式: 使用下划线前的部分")
        logger.info("4. ✅ 完善请求头: 模拟真实浏览器")
        logger.info("5. ✅ 基于网络监听的真实参数")
    else:
        logger.error("❌ 部分测试失败，请检查错误日志")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
