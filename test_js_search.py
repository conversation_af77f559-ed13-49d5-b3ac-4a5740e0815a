#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript搜索功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_js_search():
    """测试JavaScript搜索功能"""
    logger = setup_logger("test_js_search")
    
    try:
        logger.info("=" * 60)
        logger.info("测试JavaScript搜索功能（免登录）")
        logger.info("=" * 60)
        
        # 创建配置管理器和采集引擎
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 测试搜索功能
        logger.info("开始搜索测试...")
        
        def progress_callback(progress_info):
            """进度回调函数"""
            logger.info(f"进度: 第{progress_info['current_page']}/{progress_info['total_pages']}页, "
                       f"当前页{progress_info['current_items']}个, "
                       f"总计{progress_info['total_items']}个商品")
        
        # 搜索iPhone商品
        products = collector.collect_search_data(
            keyword="iPhone", 
            max_pages=2, 
            min_want_count=5,
            progress_callback=progress_callback
        )
        
        if products:
            logger.info(f"✅ 搜索成功！获取到 {len(products)} 个商品")
            
            # 显示前5个商品
            for i, product in enumerate(products[:5]):
                logger.info(f"\n商品 {i+1}:")
                logger.info(f"  标题: {product.get('title', 'N/A')}")
                logger.info(f"  价格: {product.get('price', 'N/A')}")
                logger.info(f"  想要: {product.get('wantCount', 'N/A')}")
                logger.info(f"  位置: {product.get('location', 'N/A')}")
                if product.get('itemUrl'):
                    logger.info(f"  链接: {product.get('itemUrl')[:50]}...")
        else:
            logger.error("❌ 搜索失败，未获取到商品数据")
            return False
        
        # 关闭浏览器
        collector.close()
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ 测试完成！JavaScript搜索功能正常工作")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logger("main")
    
    logger.info("开始测试JavaScript搜索功能")
    
    success = test_js_search()
    
    if success:
        logger.info("\n🎉 JavaScript搜索功能测试成功！")
        logger.info("现在可以在主程序中直接使用搜索功能，无需登录。")
    else:
        logger.error("\n❌ 测试失败，请检查错误日志。")

if __name__ == "__main__":
    main()
